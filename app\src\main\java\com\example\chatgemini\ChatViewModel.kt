package com.example.chatgemini

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.ai.client.generativeai.GenerativeModel
import kotlinx.coroutines.launch

class ChatViewModel : ViewModel() {
    private val _messages = MutableLiveData<List<ChatMessage>>()
    val messages: LiveData<List<ChatMessage>> get() = _messages
    
    private val generativeModel = GenerativeModel(
        modelName = "gemini-pro",
        apiKey = "AIzaSyB4ZGAAa9RGCIQYMmat7wkBhu92pW6q_xw"
    )

    private val messageList = mutableListOf<ChatMessage>()

    init {
        messageList.add(ChatMessage("Welcome! Ask me anything", false))
        _messages.value = messageList
    }

    fun sendMessage(input: String) {
        val userMessage = ChatMessage(input, true)
        messageList.add(userMessage)
        _messages.postValue(messageList.toList())
        
        viewModelScope.launch {
            try {
                val response = generativeModel.generateContent(input)
                val botMessage = ChatMessage(response.text ?: "Sorry, I didn't get that", false)
                messageList.add(botMessage)
                _messages.postValue(messageList.toList())
            } catch (e: Exception) {
                val errorMessage = ChatMessage("Error: ${e.message}", false)
                messageList.add(errorMessage)
                _messages.postValue(messageList.toList())
            }
        }
    }
}

data class ChatMessage(
    val content: String,
    val isUser: Boolean
)
