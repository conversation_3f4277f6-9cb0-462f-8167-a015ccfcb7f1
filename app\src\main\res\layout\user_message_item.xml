<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="8dp">

    <com.google.android.material.card.MaterialCardView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="64dp"
        android:layout_gravity="end"
        app:cardBackgroundColor="?attr/colorPrimary">

        <TextView
            android:id="@+id/tv_user_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="16dp"
            android:textColor="@android:color/white"
            android:textSize="16sp"/>

    </com.google.android.material.card.MaterialCardView>

</LinearLayout>
